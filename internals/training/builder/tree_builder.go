// Package builder provides tree construction functionality using the C4.5 splitting algorithm.
// It integrates the training package's C45Splitter with the models package's tree structures.
package builder

import (
	"context"
	"fmt"
	"strconv"

	"github.com/berrijam/mulberri/internals/training"
	"github.com/berrijam/mulberri/pkg/logger"
	"github.com/berrijam/mulberri/pkg/models"
)

// TreeBuilder constructs decision trees using the C4.5 algorithm
type TreeBuilder[T comparable] struct {
	// Core components
	nodeBuilder          NodeBuilder[T]
	statisticsCalculator StatisticsCalculator[T]
	stoppingCriteria     StoppingCriteria[T]
	configValidator      ConfigValidator
	splittingStrategy    SplittingStrategy[T]
	splitter             *training.C45Splitter[T]
	config               BuilderConfig
}

// C45SplittingStrategy wraps the C45Splitter to implement SplittingStrategy interface
type C45SplittingStrategy[T comparable] struct {
	splitter *training.C45Splitter[T]
}

// NewC45SplittingStrategy creates a C45 splitting strategy
func NewC45SplittingStrategy[T comparable](splitter *training.C45Splitter[T]) *C45SplittingStrategy[T] {
	return &C45SplittingStrategy[T]{
		splitter: splitter,
	}
}

// FindBestSplit finds the best split for the given dataset and features
func (css *C45SplittingStrategy[T]) FindBestSplit(ctx context.Context, dataset training.Dataset[T], features []*models.Feature) (*training.SplitResult[T], error) {
	return css.splitter.FindBestSplit(ctx, dataset, features)
}

// TreeConfiguration defines the interface for tree configuration
type TreeConfiguration interface {
	GetMaxDepth() int
	GetMinSamplesSplit() int
	GetMinSamplesLeaf() int
	GetMinImpurityDecrease() float64
	GetCriterion() models.SplitCriterion
	GetTargetType() models.FeatureType
	GetEnableLogging() bool
	Validate() error
	ToModelOptions() []models.TreeOption
}

// UnifiedTreeConfig provides a single source of truth for tree configuration
type UnifiedTreeConfig struct {
	MaxDepth            int                   `json:"max_depth"`
	MinSamplesSplit     int                   `json:"min_samples_split"`
	MinSamplesLeaf      int                   `json:"min_samples_leaf"`
	MinImpurityDecrease float64               `json:"min_impurity_decrease"`
	Criterion           models.SplitCriterion `json:"criterion"`
	TargetType          models.FeatureType    `json:"target_type"`
	EnableLogging       bool                  `json:"enable_logging"`
}

// BuilderConfig is kept for backward compatibility but now wraps UnifiedTreeConfig
type BuilderConfig struct {
	*UnifiedTreeConfig
}

// inferTargetType automatically infers the target type from the generic type parameter
func inferTargetType[T comparable]() models.FeatureType {
	var zero T
	switch any(zero).(type) {
	case string:
		return models.CategoricalFeature
	case int, int8, int16, int32, int64, uint, uint8, uint16, uint32, uint64, float32, float64:
		return models.NumericFeature
	default:
		return models.CategoricalFeature // Safe default for other comparable types
	}
}

// DefaultUnifiedTreeConfig returns sensible default configuration
func DefaultUnifiedTreeConfig() *UnifiedTreeConfig {
	return &UnifiedTreeConfig{
		MaxDepth:            10,
		MinSamplesSplit:     2,
		MinSamplesLeaf:      1,
		MinImpurityDecrease: 0.0,
		Criterion:           models.EntropyCriterion, // Match training package default
		TargetType:          models.CategoricalFeature,
		EnableLogging:       false,
	}
}

// DefaultUnifiedTreeConfigForType returns default configuration with inferred target type
func DefaultUnifiedTreeConfigForType[T comparable]() *UnifiedTreeConfig {
	config := DefaultUnifiedTreeConfig()
	config.TargetType = inferTargetType[T]()
	return config
}

// DefaultBuilderConfig returns sensible default configuration (backward compatibility)
func DefaultBuilderConfig() BuilderConfig {
	return BuilderConfig{
		UnifiedTreeConfig: DefaultUnifiedTreeConfig(),
	}
}

// Interface implementation for UnifiedTreeConfig
func (c *UnifiedTreeConfig) GetMaxDepth() int {
	return c.MaxDepth
}

func (c *UnifiedTreeConfig) GetMinSamplesSplit() int {
	return c.MinSamplesSplit
}

func (c *UnifiedTreeConfig) GetMinSamplesLeaf() int {
	return c.MinSamplesLeaf
}

func (c *UnifiedTreeConfig) GetMinImpurityDecrease() float64 {
	return c.MinImpurityDecrease
}

func (c *UnifiedTreeConfig) GetCriterion() models.SplitCriterion {
	return c.Criterion
}

func (c *UnifiedTreeConfig) GetTargetType() models.FeatureType {
	return c.TargetType
}

func (c *UnifiedTreeConfig) GetEnableLogging() bool {
	return c.EnableLogging
}

func (c *UnifiedTreeConfig) Validate() error {

	if c.MinSamplesLeaf > c.MinSamplesSplit {
		return &BuilderError{
			Op: "validate_config",
			Reason: fmt.Sprintf("min_samples_leaf (%d) cannot be greater than min_samples_split (%d)",
				c.MinSamplesLeaf, c.MinSamplesSplit),
		}
	}

	if c.MinImpurityDecrease < 0.0 {
		return &BuilderError{
			Op:     "validate_config",
			Field:  "min_impurity_decrease",
			Value:  fmt.Sprintf("%.6f", c.MinImpurityDecrease),
			Reason: "minimum impurity decrease cannot be negative",
		}
	}

	return nil
}

func (c *UnifiedTreeConfig) ToModelOptions() []models.TreeOption {
	return []models.TreeOption{
		models.WithMaxDepth(c.MaxDepth),
		models.WithMinSamples(c.MinSamplesSplit),
		models.WithTargetType(c.TargetType),
		models.WithCriterion(c.Criterion),
	}
}

// BuilderOption defines functional options for builder configuration
type BuilderOption func(*BuilderConfig) error

// WithMaxDepth sets the maximum tree depth
func WithMaxDepth(depth int) BuilderOption {
	return func(config *BuilderConfig) error {
		if depth <= 0 {
			return &BuilderError{
				Op:     "configure_builder",
				Field:  "max_depth",
				Value:  fmt.Sprintf("%d", depth),
				Reason: "max depth must be positive",
			}
		}
		config.MaxDepth = depth
		return nil
	}
}

// WithMinSamplesSplit sets minimum samples required to split a node
// Includes cross-parameter validation with MinSamplesLeaf
func WithMinSamplesSplit(samples int) BuilderOption {
	return func(config *BuilderConfig) error {
		if samples < 2 {
			return &BuilderError{
				Op:     "configure_builder",
				Field:  "min_samples_split",
				Value:  fmt.Sprintf("%d", samples),
				Reason: "minimum samples split must be >= 2",
			}
		}

		// Cross-parameter validation: MinSamplesSplit cannot be less than MinSamplesLeaf
		if config.MinSamplesLeaf > 0 && samples < config.MinSamplesLeaf {
			return &BuilderError{
				Op:    "configure_builder",
				Field: "min_samples_split",
				Value: fmt.Sprintf("%d", samples),
				Reason: fmt.Sprintf("min_samples_split (%d) cannot be less than min_samples_leaf (%d)",
					samples, config.MinSamplesLeaf),
			}
		}

		config.MinSamplesSplit = samples
		return nil
	}
}

// WithMinSamplesLeaf sets minimum samples required in a leaf node
// Includes cross-parameter validation with MinSamplesSplit
func WithMinSamplesLeaf(samples int) BuilderOption {
	return func(config *BuilderConfig) error {
		if samples < 1 {
			return &BuilderError{
				Op:     "configure_builder",
				Field:  "min_samples_leaf",
				Value:  fmt.Sprintf("%d", samples),
				Reason: "minimum samples leaf must be >= 1",
			}
		}

		// Cross-parameter validation: MinSamplesLeaf cannot exceed MinSamplesSplit
		if config.MinSamplesSplit > 0 && samples > config.MinSamplesSplit {
			return &BuilderError{
				Op:    "configure_builder",
				Field: "min_samples_leaf",
				Value: fmt.Sprintf("%d", samples),
				Reason: fmt.Sprintf("min_samples_leaf (%d) cannot exceed min_samples_split (%d)",
					samples, config.MinSamplesSplit),
			}
		}

		config.MinSamplesLeaf = samples
		return nil
	}
}

// WithMinImpurityDecrease sets minimum impurity decrease required for split
func WithMinImpurityDecrease(decrease float64) BuilderOption {
	return func(config *BuilderConfig) error {
		if decrease < 0.0 {
			return &BuilderError{
				Op:     "configure_builder",
				Field:  "min_impurity_decrease",
				Value:  fmt.Sprintf("%.6f", decrease),
				Reason: "minimum impurity decrease cannot be negative",
			}
		}
		config.MinImpurityDecrease = decrease
		return nil
	}
}

// WithCriterion sets the split criterion
func WithCriterion(criterion models.SplitCriterion) BuilderOption {
	return func(config *BuilderConfig) error {
		switch criterion {
		case models.GiniCriterion, models.EntropyCriterion, models.MSECriterion:
			config.Criterion = criterion
			return nil
		default:
			return &BuilderError{
				Op:     "configure_builder",
				Field:  "criterion",
				Value:  string(criterion),
				Reason: "invalid split criterion, must be 'gini', 'entropy', or 'mse'",
			}
		}
	}
}

// WithTargetType sets the target variable type
func WithTargetType(targetType models.FeatureType) BuilderOption {
	return func(config *BuilderConfig) error {
		config.TargetType = targetType
		return nil
	}
}

// WithLogging enables or disables logging
func WithLogging(enabled bool) BuilderOption {
	return func(config *BuilderConfig) error {
		config.EnableLogging = enabled
		return nil
	}
}

// NewTreeBuilder creates a new tree builder with the given splitter and options
func NewTreeBuilder[T comparable](splitter *training.C45Splitter[T], options ...BuilderOption) (*TreeBuilder[T], error) {
	if splitter == nil {
		return nil, &BuilderError{
			Op:     "create_builder",
			Field:  "splitter",
			Reason: "splitter cannot be nil",
		}
	}

	config := DefaultBuilderConfig()

	// Apply functional options
	for _, option := range options {
		if err := option(&config); err != nil {
			return nil, &BuilderError{
				Op:     "create_builder",
				Reason: fmt.Sprintf("invalid option: %v", err),
				Err:    err,
			}
		}
	}

	// Validate unified configuration
	if err := config.UnifiedTreeConfig.Validate(); err != nil {
		return nil, &BuilderError{
			Op:     "create_builder",
			Reason: fmt.Sprintf("configuration validation failed: %v", err),
			Err:    err,
		}
	}

	// Create components
	nodeBuilder := NewDefaultNodeBuilder[T](config.UnifiedTreeConfig)
	statisticsCalculator := NewDefaultStatisticsCalculator[T](config.UnifiedTreeConfig)
	stoppingCriteria := NewDefaultStoppingCriteria[T](config.UnifiedTreeConfig)
	configValidator := NewDefaultConfigValidator(config.UnifiedTreeConfig)
	splittingStrategy := NewC45SplittingStrategy[T](splitter)

	// Create builder instance
	builder := &TreeBuilder[T]{
		nodeBuilder:          nodeBuilder,
		statisticsCalculator: statisticsCalculator,
		stoppingCriteria:     stoppingCriteria,
		configValidator:      configValidator,
		splittingStrategy:    splittingStrategy,
		splitter:             splitter,
		config:               config,
	}

	// Validate configuration
	if err := builder.configValidator.ValidateConfiguration(); err != nil {
		return nil, &BuilderError{
			Op:     "create_builder",
			Reason: fmt.Sprintf("configuration validation failed: %v", err),
			Err:    err,
		}
	}

	// Validate type compatibility
	if err := builder.validateTypeCompatibility(); err != nil {
		return nil, &BuilderError{
			Op:     "create_builder",
			Reason: fmt.Sprintf("type compatibility validation failed: %v", err),
			Err:    err,
		}
	}

	return builder, nil
}

// NewTreeBuilderWithInference creates a tree builder with automatic target type inference
func NewTreeBuilderWithInference[T comparable](splitter *training.C45Splitter[T], options ...BuilderOption) (*TreeBuilder[T], error) {
	// Start with type-aware default configuration
	config := BuilderConfig{
		UnifiedTreeConfig: DefaultUnifiedTreeConfigForType[T](),
	}

	// Apply functional options
	for _, option := range options {
		if err := option(&config); err != nil {
			return nil, &BuilderError{
				Op:     "create_builder_with_inference",
				Reason: fmt.Sprintf("invalid option: %v", err),
				Err:    err,
			}
		}
	}

	// Use the standard constructor with the configured options
	return NewTreeBuilder(splitter, func(c *BuilderConfig) error {
		*c = config
		return nil
	})
}

// BuildTree constructs a complete decision tree from the dataset
func (b *TreeBuilder[T]) BuildTree(ctx context.Context, dataset training.Dataset[T], features []*models.Feature) (*models.DecisionTree, error) {
	if err := b.validateBuildInputs(dataset, features); err != nil {
		return nil, err
	}

	if b.config.GetEnableLogging() {
		logger.Info(fmt.Sprintf("Starting tree construction with %d samples and %d features", dataset.GetSize(), len(features)))
	}

	// Create the decision tree container
	tree, err := b.createDecisionTreeContainer(features)
	if err != nil {
		return nil, &BuilderError{
			Op:     "build_tree",
			Reason: fmt.Sprintf("failed to create tree container: %v", err),
			Err:    err,
		}
	}

	// Build the root node
	root, err := b.buildNode(ctx, dataset, features, 0, "root")
	if err != nil {
		return nil, &BuilderError{
			Op:     "build_tree",
			Reason: fmt.Sprintf("failed to build root node: %v", err),
			Err:    err,
		}
	}

	tree.Root = root
	tree.UpdateStatistics()

	if b.config.GetEnableLogging() {
		logger.Info(fmt.Sprintf("Tree construction completed: %d nodes, %d leaves, depth %d", tree.NodeCount, tree.LeafCount, tree.Depth))
	}

	return tree, nil
}

// validateBuildInputs performs input validation
func (b *TreeBuilder[T]) validateBuildInputs(dataset training.Dataset[T], features []*models.Feature) error {
	var errors training.MultiError

	// Dataset validation
	if dataset == nil {
		errors.Add(&BuilderError{
			Op:     "validate_build_inputs",
			Field:  "dataset",
			Reason: "dataset cannot be nil",
		})
	} else {
		if dataset.GetSize() == 0 {
			errors.Add(&BuilderError{
				Op:     "validate_build_inputs",
				Field:  "dataset",
				Reason: "dataset cannot be empty",
			})
		} else {
			// Check dataset size against configuration
			datasetSize := dataset.GetSize()
			if datasetSize < b.config.GetMinSamplesSplit() {
				errors.Add(&BuilderError{
					Op:    "validate_build_inputs",
					Field: "dataset_size",
					Value: fmt.Sprintf("%d", datasetSize),
					Reason: fmt.Sprintf("dataset size (%d) is smaller than min_samples_split (%d)",
						datasetSize, b.config.GetMinSamplesSplit()),
				})
			}

			// Validate that dataset has sufficient samples for meaningful tree
			if datasetSize < 2*b.config.GetMinSamplesLeaf() {
				errors.Add(&BuilderError{
					Op:    "validate_build_inputs",
					Field: "dataset_size",
					Value: fmt.Sprintf("%d", datasetSize),
					Reason: fmt.Sprintf("dataset size (%d) is too small for meaningful tree construction (need at least %d samples)",
						datasetSize, 2*b.config.GetMinSamplesLeaf()),
				})
			}
		}
	}

	// Features validation
	if len(features) == 0 {
		errors.Add(&BuilderError{
			Op:     "validate_build_inputs",
			Field:  "features",
			Reason: "features cannot be empty",
		})
	} else {
		// Check for feature name uniqueness
		featureNames := make(map[string]int)

		// Validate each feature
		for i, feature := range features {
			if feature == nil {
				errors.Add(&BuilderError{
					Op:     "validate_build_inputs",
					Field:  "features",
					Value:  fmt.Sprintf("%d", i),
					Reason: fmt.Sprintf("feature at index %d is nil", i),
				})
				continue
			}

			// Check for duplicate feature names
			if prevIndex, exists := featureNames[feature.Name]; exists {
				errors.Add(&BuilderError{
					Op:    "validate_build_inputs",
					Field: "feature_names",
					Value: feature.Name,
					Reason: fmt.Sprintf("duplicate feature name '%s' found at indices %d and %d",
						feature.Name, prevIndex, i),
				})
			} else {
				featureNames[feature.Name] = i
			}

			// Validate individual feature
			if err := feature.Validate(); err != nil {
				errors.Add(&BuilderError{
					Op:     "validate_build_inputs",
					Field:  "feature_validation",
					Value:  feature.Name,
					Reason: fmt.Sprintf("feature '%s' validation failed: %v", feature.Name, err),
					Err:    err,
				})
			}

			// Check feature column bounds
			if feature.ColumnNumber < 0 {
				errors.Add(&BuilderError{
					Op:     "validate_build_inputs",
					Field:  "feature_column",
					Value:  fmt.Sprintf("%d", feature.ColumnNumber),
					Reason: fmt.Sprintf("feature '%s' has invalid column number %d", feature.Name, feature.ColumnNumber),
				})
			}
		}
	}

	// Cross-validation between dataset and configuration
	if dataset != nil && len(features) > 0 {
		// Validate that the configuration is reasonable for the dataset size
		if err := b.configValidator.ValidateForDataset(dataset.GetSize(), len(features)); err != nil {
			errors.Add(&BuilderError{
				Op:     "validate_build_inputs",
				Field:  "config_dataset_compatibility",
				Reason: fmt.Sprintf("configuration incompatible with dataset: %v", err),
				Err:    err,
			})
		}
	}

	if errors.HasErrors() {
		return &BuilderError{
			Op:     "validate_build_inputs",
			Reason: "input validation failed",
			Err:    &errors,
		}
	}

	return nil
}

// createDecisionTreeContainer creates the tree container with proper feature setup
func (b *TreeBuilder[T]) createDecisionTreeContainer(features []*models.Feature) (*models.DecisionTree, error) {
	// Use unified configuration to create tree with consistent options
	tree, err := models.NewDecisionTreeWithOptions(b.config.UnifiedTreeConfig.ToModelOptions()...)
	if err != nil {
		return nil, &BuilderError{
			Op:     "create_tree_container",
			Field:  "tree_creation",
			Reason: fmt.Sprintf("failed to create tree with unified config: %v", err),
			Err:    err,
		}
	}

	// Add features to the tree
	for i, feature := range features {
		logger.Debug(feature.Name + string(feature.Type) + strconv.Itoa(i))
		_, err := tree.AddFeature(feature.Name, feature.Type)
		if err != nil {
			return nil, &BuilderError{
				Op:     "create_tree_container",
				Field:  "add_feature",
				Value:  feature.Name,
				Reason: fmt.Sprintf("failed to add feature: %v", err),
				Err:    err,
			}
		}
	}

	return tree, nil
}

// buildNode recursively constructs tree nodes
func (b *TreeBuilder[T]) buildNode(ctx context.Context, dataset training.Dataset[T], features []*models.Feature, depth int, nodeID string) (*models.TreeNode, error) {
	// Check context cancellation
	select {
	case <-ctx.Done():
		return nil, ctx.Err()
	default:
	}

	if b.config.GetEnableLogging() && depth <= 2 {
		logger.Debug(fmt.Sprintf("Building node %s at depth %d with %d samples", nodeID, depth, dataset.GetSize()))
	}

	// Calculate node statistics
	nodeStats, err := b.calculateNodeStatistics(dataset)
	if err != nil {
		return nil, &BuilderError{
			Op:     "build_node",
			Field:  "node_statistics",
			Reason: fmt.Sprintf("failed to calculate node statistics: %v", err),
			Err:    err,
		}
	}

	// Check stopping criteria
	if b.shouldCreateLeaf(dataset, depth, nodeStats.impurity) {
		return b.createLeafNode(nodeStats)
	}

	// Find best split using the C4.5 splitter
	split, err := b.splitter.FindBestSplit(ctx, dataset, features)
	if err != nil {
		return nil, &BuilderError{
			Op:     "build_node",
			Field:  "find_split",
			Reason: fmt.Sprintf("failed to find split: %v", err),
			Err:    err,
		}
	}

	// If no beneficial split found, create leaf
	if split == nil || split.GainRatio <= b.config.GetMinImpurityDecrease() {
		if b.config.GetEnableLogging() && depth <= 2 {
			logger.Debug(fmt.Sprintf("No beneficial split found for node %s, creating leaf", nodeID))
		}
		return b.createLeafNode(nodeStats)
	}

	// Create decision node from split
	return b.createDecisionNodeFromSplit(ctx, split, dataset, features, depth, nodeID, nodeStats)
}

// shouldCreateLeaf determines if a leaf node should be created
func (b *TreeBuilder[T]) shouldCreateLeaf(dataset training.Dataset[T], depth int, impurity float64) bool {
	return b.stoppingCriteria.ShouldCreateLeaf(dataset, depth, impurity)
}

// nodeStatistics holds calculated statistics for a node
type nodeStatistics[T comparable] struct {
	sampleCount       int
	classDistribution map[T]int
	majorityClass     T
	confidence        float64
	impurity          float64
}

// calculateNodeStatistics computes statistics for the current dataset
func (b *TreeBuilder[T]) calculateNodeStatistics(dataset training.Dataset[T]) (*nodeStatistics[T], error) {
	// Use the statistics calculator
	stats, err := b.statisticsCalculator.CalculateNodeStatistics(dataset)
	if err != nil {
		return nil, err
	}

	return &nodeStatistics[T]{
		sampleCount:       stats.SampleCount,
		classDistribution: stats.ClassDistribution,
		majorityClass:     stats.MajorityClass,
		confidence:        stats.Confidence,
		impurity:          stats.Impurity,
	}, nil
}

// createLeafNode creates a leaf node with prediction
func (b *TreeBuilder[T]) createLeafNode(stats *nodeStatistics[T]) (*models.TreeNode, error) {
	newStats := &NodeStatistics[T]{
		SampleCount:       stats.sampleCount,
		ClassDistribution: stats.classDistribution,
		MajorityClass:     stats.majorityClass,
		Confidence:        stats.confidence,
		Impurity:          stats.impurity,
	}

	// Use the node builder
	return b.nodeBuilder.BuildLeafNode(newStats)
}

// convertClassDistribution safely converts generic class distribution to interface{} map
// This provides a single point of type conversion with clear semantics and maintains
// type safety by centralizing the conversion logic. This addresses the type safety
// issue identified in the code review by eliminating scattered interface{} conversions.
func (b *TreeBuilder[T]) convertClassDistribution(distribution map[T]int) map[interface{}]int {
	if distribution == nil {
		return make(map[interface{}]int)
	}

	result := make(map[interface{}]int, len(distribution))
	for class, count := range distribution {
		result[interface{}(class)] = count
	}
	return result
}

// convertPrediction safely converts generic prediction to interface{}
// This provides a single point of type conversion with clear semantics and maintains
// compile-time type safety within the builder while providing compatibility with
// the existing models package interface.
func (b *TreeBuilder[T]) convertPrediction(prediction T) interface{} {
	return interface{}(prediction)
}

// validateTypeCompatibility ensures the generic type T is compatible with the target type
func (b *TreeBuilder[T]) validateTypeCompatibility() error {
	var zero T
	targetType := b.config.GetTargetType()

	switch targetType {
	case models.CategoricalFeature:
		// Categorical features can accept any comparable type
		return nil
	case models.NumericFeature:
		// Numeric features should only accept numeric types
		switch any(zero).(type) {
		case int, int8, int16, int32, int64, uint, uint8, uint16, uint32, uint64, float32, float64:
			return nil
		default:
			return &BuilderError{
				Op:     "validate_type_compatibility",
				Field:  "target_type",
				Value:  string(targetType),
				Reason: fmt.Sprintf("generic type %T is not compatible with numeric target type", zero),
			}
		}
	default:
		return &BuilderError{
			Op:     "validate_type_compatibility",
			Field:  "target_type",
			Value:  string(targetType),
			Reason: "unsupported target type",
		}
	}
}

// createDecisionNodeFromSplit creates a decision node and its children from a split result
func (b *TreeBuilder[T]) createDecisionNodeFromSplit(ctx context.Context, split *training.SplitResult[T], dataset training.Dataset[T], features []*models.Feature, depth int, nodeID string, stats *nodeStatistics[T]) (*models.TreeNode, error) {
	var node *models.TreeNode
	var err error

	// Create appropriate node type based on feature type
	switch split.Feature.Type {
	case models.NumericFeature, models.DateFeature:
		node, err = models.NewDecisionNode(split.Feature, split.Threshold)
		if err != nil {
			return nil, &BuilderError{
				Op:     "create_decision_node",
				Field:  "numeric_node",
				Reason: fmt.Sprintf("failed to create numeric decision node: %v", err),
				Err:    err,
			}
		}

		// Build children for binary split
		if err := b.buildBinaryChildren(ctx, node, split, dataset, features, depth, nodeID); err != nil {
			return nil, err
		}

	case models.CategoricalFeature:
		node, err = models.NewCategoricalDecisionNode(split.Feature)
		if err != nil {
			return nil, &BuilderError{
				Op:     "create_decision_node",
				Field:  "categorical_node",
				Reason: fmt.Sprintf("failed to create categorical decision node: %v", err),
				Err:    err,
			}
		}

		// Build children for categorical split
		if err := b.buildCategoricalChildren(ctx, node, split, dataset, features, depth, nodeID); err != nil {
			return nil, err
		}

	default:
		return nil, &BuilderError{
			Op:     "create_decision_node",
			Field:  "feature_type",
			Value:  string(split.Feature.Type),
			Reason: "unsupported feature type for decision node",
		}
	}

	// Set node statistics
	node.Samples = stats.sampleCount
	node.Confidence = stats.confidence
	node.Impurity = stats.impurity

	// Convert and set class distribution using type-safe helper
	node.ClassDistribution = b.convertClassDistribution(stats.classDistribution)

	return node, nil
}

// buildBinaryChildren builds left and right children for numeric splits
func (b *TreeBuilder[T]) buildBinaryChildren(ctx context.Context, node *models.TreeNode, split *training.SplitResult[T], dataset training.Dataset[T], features []*models.Feature, depth int, nodeID string) error {
	// Build left child (values <= threshold)
	if len(split.LeftIndices) >= b.config.GetMinSamplesLeaf() {
		leftDataset := dataset.Subset(split.LeftIndices)
		leftChild, err := b.buildNode(ctx, leftDataset, features, depth+1, nodeID+"_L")
		if err != nil {
			return &BuilderError{
				Op:     "build_binary_children",
				Field:  "left_child",
				Reason: fmt.Sprintf("failed to build left child: %v", err),
				Err:    err,
			}
		}

		if err := node.SetLeftChild(leftChild); err != nil {
			return &BuilderError{
				Op:     "build_binary_children",
				Field:  "set_left_child",
				Reason: fmt.Sprintf("failed to set left child: %v", err),
				Err:    err,
			}
		}
	}

	// Build right child (values > threshold)
	if len(split.RightIndices) >= b.config.GetMinSamplesLeaf() {
		rightDataset := dataset.Subset(split.RightIndices)
		rightChild, err := b.buildNode(ctx, rightDataset, features, depth+1, nodeID+"_R")
		if err != nil {
			return &BuilderError{
				Op:     "build_binary_children",
				Field:  "right_child",
				Reason: fmt.Sprintf("failed to build right child: %v", err),
				Err:    err,
			}
		}

		if err := node.SetRightChild(rightChild); err != nil {
			return &BuilderError{
				Op:     "build_binary_children",
				Field:  "set_right_child",
				Reason: fmt.Sprintf("failed to set right child: %v", err),
				Err:    err,
			}
		}
	}

	return nil
}

// buildCategoricalChildren builds children for categorical splits
func (b *TreeBuilder[T]) buildCategoricalChildren(ctx context.Context, node *models.TreeNode, split *training.SplitResult[T], dataset training.Dataset[T], features []*models.Feature, depth int, nodeID string) error {
	childIndex := 0
	var errors training.MultiError

	for category, indices := range split.Partitions {
		if len(indices) >= b.config.GetMinSamplesLeaf() {
			childDataset := dataset.Subset(indices)
			childNodeID := fmt.Sprintf("%s_C%d", nodeID, childIndex)

			child, err := b.buildNode(ctx, childDataset, features, depth+1, childNodeID)
			if err != nil {
				errors.Add(&BuilderError{
					Op:     "build_categorical_children",
					Field:  "child_node",
					Value:  fmt.Sprintf("%v", category),
					Reason: fmt.Sprintf("failed to build child for category: %v", err),
					Err:    err,
				})
				continue
			}

			if err := node.SetChild(category, child); err != nil {
				errors.Add(&BuilderError{
					Op:     "build_categorical_children",
					Field:  "set_child",
					Value:  fmt.Sprintf("%v", category),
					Reason: fmt.Sprintf("failed to set child: %v", err),
					Err:    err,
				})
				continue
			}

			childIndex++
		}
	}

	if errors.HasErrors() {
		return &BuilderError{
			Op:     "build_categorical_children",
			Reason: "failed to build categorical children",
			Err:    &errors,
		}
	}

	return nil
}

// GetConfig returns the current builder configuration
func (b *TreeBuilder[T]) GetConfig() BuilderConfig {
	return b.config
}

// GetSplitter returns the underlying splitter (for debugging/testing)
func (b *TreeBuilder[T]) GetSplitter() *training.C45Splitter[T] {
	return b.splitter
}

// GetNodeBuilder returns the node builder component
func (b *TreeBuilder[T]) GetNodeBuilder() NodeBuilder[T] {
	return b.nodeBuilder
}

// GetStatisticsCalculator returns the statistics calculator component
func (b *TreeBuilder[T]) GetStatisticsCalculator() StatisticsCalculator[T] {
	return b.statisticsCalculator
}

// GetStoppingCriteria returns the stopping criteria component
func (b *TreeBuilder[T]) GetStoppingCriteria() StoppingCriteria[T] {
	return b.stoppingCriteria
}

// GetConfigValidator returns the config validator component
func (b *TreeBuilder[T]) GetConfigValidator() ConfigValidator {
	return b.configValidator
}

// GetSplittingStrategy returns the splitting strategy component
func (b *TreeBuilder[T]) GetSplittingStrategy() SplittingStrategy[T] {
	return b.splittingStrategy
}
