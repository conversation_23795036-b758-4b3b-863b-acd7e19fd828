// Package utils provides structured dataset implementation for memory-efficient CSV processing.
package utils

import (
	"fmt"
	"strconv"

	"github.com/berrijam/mulberri/internals/training"
	"github.com/berrijam/mulberri/pkg/logger"
	"github.com/berrijam/mulberri/pkg/models"
)

// TrainingRecord represents a single training sample with typed features and target
type TrainingRecord struct {
	Features map[string]interface{} // Feature name -> typed value
	Target   string                 // Target value
}

// StructuredDataset implements training.Dataset[string] using structured records instead of arrays
// This eliminates the need for intermediate string arrays and reduces memory copying
type StructuredDataset struct {
	records         []TrainingRecord
	featureHeaders  []string
	featureIndexMap map[string]int // Maps feature name to column index for compatibility
	indices         []int
}

// NewStructuredDataset creates a new structured dataset with initial capacity
func NewStructuredDataset(featureHeaders []string, initialCapacity int) *StructuredDataset {
	// Create feature index map for O(1) lookups
	featureIndexMap := make(map[string]int, len(featureHeaders))
	for i, header := range featureHeaders {
		featureIndexMap[header] = i
	}

	return &StructuredDataset{
		records:         make([]TrainingRecord, 0, initialCapacity),
		featureHeaders:  featureHeaders,
		featureIndexMap: featureIndexMap,
		indices:         make([]int, 0, initialCapacity),
	}
}

// AddRecord adds a new training record with typed features during streaming
func (d *StructuredDataset) AddRecord(featureValues map[string]interface{}, target string) error {
	record := TrainingRecord{
		Features: featureValues,
		Target:   target,
	}
	
	d.records = append(d.records, record)
	d.indices = append(d.indices, len(d.records)-1)
	
	return nil
}

// GetSize returns the current number of records
func (d *StructuredDataset) GetSize() int {
	return len(d.indices)
}

// GetFeatureValue retrieves a feature value for a given sample and feature
// This method provides direct access to typed values without string conversion
func (d *StructuredDataset) GetFeatureValue(sampleIdx int, feature *models.Feature) (interface{}, error) {
	if sampleIdx < 0 || sampleIdx >= len(d.indices) {
		return nil, fmt.Errorf("sample index %d out of range [0, %d)", sampleIdx, len(d.indices))
	}

	actualIdx := d.indices[sampleIdx]
	if actualIdx >= len(d.records) {
		return nil, fmt.Errorf("internal error: index %d out of range for records", actualIdx)
	}

	record := d.records[actualIdx]
	
	// Try to get value by feature name first (preferred)
	if value, exists := record.Features[feature.Name]; exists {
		return value, nil
	}

	// Fallback: try to get by column number if feature name not found
	// This maintains compatibility with existing code that uses column numbers
	if feature.ColumnNumber < len(d.featureHeaders) {
		featureName := d.featureHeaders[feature.ColumnNumber]
		if value, exists := record.Features[featureName]; exists {
			return value, nil
		}
	}

	return nil, fmt.Errorf("feature '%s' (column %d) not found in record", feature.Name, feature.ColumnNumber)
}

// GetTarget retrieves the target value for a given sample
func (d *StructuredDataset) GetTarget(sampleIdx int) (string, error) {
	if sampleIdx < 0 || sampleIdx >= len(d.indices) {
		return "", fmt.Errorf("sample index %d out of range [0, %d)", sampleIdx, len(d.indices))
	}

	actualIdx := d.indices[sampleIdx]
	if actualIdx >= len(d.records) {
		return "", fmt.Errorf("internal error: index %d out of range for records", actualIdx)
	}

	return d.records[actualIdx].Target, nil
}

// GetIndices returns the current sample indices
func (d *StructuredDataset) GetIndices() []int {
	return d.indices
}

// Subset creates a new dataset view with the specified indices
// This is a zero-copy operation that creates a new view without duplicating data
func (d *StructuredDataset) Subset(indices []int) training.Dataset[string] {
	return &StructuredDataset{
		records:         d.records,
		featureHeaders:  d.featureHeaders,
		featureIndexMap: d.featureIndexMap,
		indices:         indices,
	}
}

// GetFeatureHeaders returns the feature headers for this dataset
func (d *StructuredDataset) GetFeatureHeaders() []string {
	return d.featureHeaders
}

// StreamingCSVBuilder builds a StructuredDataset during CSV parsing
type StreamingCSVBuilder struct {
	dataset         *StructuredDataset
	featureTypes    map[string]models.FeatureType
	featureHeaders  []string
	targetColumn    string
}

// NewStreamingCSVBuilder creates a new builder for streaming CSV parsing
func NewStreamingCSVBuilder(headers []string, targetColumn string, featureTypes map[string]models.FeatureType, initialCapacity int) (*StreamingCSVBuilder, error) {
	// Extract feature headers (exclude target column)
	featureHeaders := make([]string, 0, len(headers)-1)
	targetFound := false
	
	for _, header := range headers {
		if header == targetColumn {
			targetFound = true
		} else {
			featureHeaders = append(featureHeaders, header)
		}
	}
	
	if !targetFound {
		return nil, fmt.Errorf("target column '%s' not found in headers", targetColumn)
	}

	dataset := NewStructuredDataset(featureHeaders, initialCapacity)
	
	return &StreamingCSVBuilder{
		dataset:         dataset,
		featureTypes:    featureTypes,
		featureHeaders:  featureHeaders,
		targetColumn:    targetColumn,
	}, nil
}

// ProcessRecord processes a CSV record and adds it to the dataset with proper type conversion
func (b *StreamingCSVBuilder) ProcessRecord(record []string, headers []string) error {
	if len(record) != len(headers) {
		return fmt.Errorf("record length %d does not match headers length %d", len(record), len(headers))
	}

	// Extract target value
	targetIdx := -1
	for i, header := range headers {
		if header == b.targetColumn {
			targetIdx = i
			break
		}
	}
	
	if targetIdx == -1 {
		return fmt.Errorf("target column '%s' not found", b.targetColumn)
	}
	
	target := record[targetIdx]

	// Process features with type conversion
	features := make(map[string]interface{}, len(b.featureHeaders))
	
	for i, header := range headers {
		if i == targetIdx {
			continue // Skip target column
		}
		
		value := record[i]
		
		// Handle NA values
		if isNAValue(value) {
			features[header] = nil
			continue
		}
		
		// Convert based on feature type
		if featureType, exists := b.featureTypes[header]; exists {
			convertedValue, err := convertFeatureValue(value, featureType)
			if err != nil {
				return fmt.Errorf("failed to convert feature '%s' value '%s': %v", header, value, err)
			}
			features[header] = convertedValue
		} else {
			// Default to string if type not specified
			features[header] = value
		}
	}

	return b.dataset.AddRecord(features, target)
}

// GetDataset returns the built dataset
func (b *StreamingCSVBuilder) GetDataset() training.Dataset[string] {
	return b.dataset
}

// convertFeatureValue converts a string value to the appropriate type
func convertFeatureValue(value string, featureType models.FeatureType) (interface{}, error) {
	switch featureType {
	case models.NumericFeature:
		if floatValue, err := strconv.ParseFloat(value, 64); err == nil {
			return floatValue, nil
		} else {
			return nil, fmt.Errorf("invalid numeric value: %s", value)
		}
	case models.CategoricalFeature:
		return value, nil
	case models.DateFeature:
		// For now, treat dates as strings. Could be enhanced with actual date parsing
		return value, nil
	default:
		return value, nil
	}
}

// isNAValue checks if a value represents a missing/NA value
func isNAValue(value string) bool {
	switch value {
	case "", "NA", "N/A", "na", "n/a", "NaN", "nan", "NULL", "null":
		return true
	default:
		return false
	}
}
